# Nhật ký thay đổi

Tất cả những thay đổi đáng chú ý đối với ứng dụng Form Management sẽ được ghi lại trong tệp này.

Định dạng dựa trên [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
và dự án này tuân theo [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Chưa phát hành]

### Thêm mới
- Tệp tài liệu cho tổng quan dự án và phát triển
- Hướng dẫn sử dụng cho ứng dụng
- Chức năng Cài đặt Người duyệt & Người thực thi với tích hợp API
  - Thêm/xóa người duyệt và người thực thi cho các loại yêu cầu và nền tảng khác nhau
  - Tìm kiếm người dùng với tự động hoàn thành từ dữ liệu API
  - Xác thực và xử lý lỗi theo thời gian thực
  - Tự động làm mới giao diện người dùng sau khi thay đổi

## [0.1.0] - 2023-11-15

### Thêm mới
- Cấu trúc ứng dụng ban đầu với Create React App
- Hệ thống xác thực với tích hợp LDAP
- Kiểm soát truy cập dựa trên vai trò (Admin, Approver, User)
- Bảng điều khiển với các chỉ số quan trọng
- Hệ thống quản lý yêu cầu với nhiều loại yêu cầu:
  - Yêu cầu truy cập nền tảng
  - Yêu cầu truy cập cơ sở dữ liệu
  - Yêu cầu dữ liệu Adhoc
  - Yêu cầu truy cập báo cáo
- Theo dõi yêu cầu với cập nhật trạng thái
- Nền tảng Whitelist với giao diện truy vấn SQL
- Trình tạo truy vấn để tạo truy vấn trực quan
- Lịch sử truy vấn và truy vấn đã lưu
- Chuyển đổi chủ đề Sáng/Tối
- Chuyển đổi ngôn ngữ (Tiếng Anh/Tiếng Việt)
- Hệ thống thông báo
- Điều hướng thanh bên đáp ứng
- Tùy chọn xem dạng lưới và Kanban cho yêu cầu
- Chức năng tìm kiếm
- Quản lý người dùng cho quản trị viên
- Trang cài đặt cho tùy chọn người dùng
- Tính năng Hỏi AI để hỗ trợ

### Thay đổi
- Không áp dụng (Phiên bản ban đầu)

### Sửa lỗi
- Không áp dụng (Phiên bản ban đầu)

### Bảo mật
- Xác thực dựa trên JWT với thời gian hết hạn token
- Hạn chế truy cập dựa trên vai trò
- Giao tiếp API an toàn
