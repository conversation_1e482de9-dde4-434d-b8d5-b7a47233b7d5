# Changelog

All notable changes to the Form Management application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Documentation files for project overview and development
- User guide for application usage
- Approver & Executor Settings functionality with API integration
  - Add/delete approvers and executors for different request types and platforms
  - Added Reviewers setting for Access Report request type
  - User search with autocomplete from API data
  - Real-time validation and error handling
  - Automatic UI refresh after changes

## [0.1.0] - 2023-11-15

### Added
- Initial application structure with Create React App
- Authentication system with LDAP integration
- Role-based access control (Admin, Approver, User)
- Dashboard with key metrics
- Request management system with multiple request types:
  - Platform Access Requests
  - Database Access Requests
  - Adhoc Data Requests
  - Report Access Requests
- Request tracking with status updates
- Whitelist Platform with SQL query interface
- Query Builder for visual query creation
- Query history and saved queries
- Dark/Light theme toggle
- Language switching (English/Vietnamese)
- Notification system
- Responsive sidebar navigation
- Grid and Kanban view options for requests
- Search functionality
- User management for administrators
- Settings page for user preferences
- Ask AI feature for assistance

### Changed
- N/A (Initial release)

### Fixed
- N/A (Initial release)

### Security
- JWT-based authentication with token expiration
- Role-based access restrictions
- Secure API communication
