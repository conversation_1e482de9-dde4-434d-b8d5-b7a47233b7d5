.feedback-container {
  max-width: 800px;
  margin: 0 auto;
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-green), #00b85e);
  border-radius: 12px;
  color: white;
}

.header-text h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color);
}

.header-text p {
  margin: 4px 0 0 0;
  font-size: 16px;
  color: var(--text-muted);
}

.feedback-form-container {
  background-color: var(--content-bg);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 30px;
  border: 1px solid var(--border-color);
}

.feedback-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(0, 207, 106, 0.1);
  outline: none;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.description-editor {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--input-bg);
}

.description-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--section-bg);
}

.description-editor .ql-container {
  border: none;
  min-height: 120px;
  font-size: 14px;
}

.attachment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-text);
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
  animation: fadeIn 0.3s ease-in-out;
}

.attachment-error-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.attachment-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  background-color: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  width: fit-content;
}

.attachment-button:hover {
  border-color: var(--primary-green);
  color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: var(--section-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 13px;
  position: relative;
}

.attachment-item.uploading {
  border-color: var(--primary-green);
  background-color: rgba(0, 207, 106, 0.05);
}

.attachment-item.uploaded {
  border-color: var(--success-text);
  background-color: var(--success-bg);
}

.attachment-item.error {
  border-color: var(--error-text);
  background-color: var(--error-bg);
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding-right: 32px; /* Add space for remove button */
}

.attachment-name {
  flex: 1;
  color: var(--text-color);
  font-weight: 500;
}

.attachment-size {
  color: var(--text-muted);
}

.upload-progress {
  color: var(--primary-green);
  font-weight: 500;
  font-size: 12px;
}

.upload-status {
  font-weight: 500;
  font-size: 12px;
}

.upload-status.success {
  color: var(--success-text);
}

.upload-status.error {
  color: var(--error-text);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), #00b85e);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.file-error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-text);
  border-radius: 4px;
  margin-top: 4px;
}

.error-text {
  flex: 1;
  color: var(--error-text);
  font-size: 12px;
  line-height: 1.4;
}

.retry-upload-btn {
  padding: 4px 8px;
  background-color: var(--error-text);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.retry-upload-btn:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

.remove-attachment {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
  position: absolute;
  top: 8px;
  right: 8px;
}

.remove-attachment:hover:not(:disabled) {
  background-color: var(--error-bg);
  color: var(--error-text);
}

.remove-attachment:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.error-message,
.success-message,
.info-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
  animation: fadeIn 0.3s ease-in-out;
}

.error-message {
  background-color: var(--error-bg);
  color: var(--error-text);
  border: 1px solid var(--error-text);
}

.success-message {
  background-color: var(--success-bg);
  color: var(--success-text);
  border: 1px solid var(--success-text);
}

.info-message {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border: 1px solid #3b82f6;
  justify-content: space-between;
}

.clear-draft-btn {
  padding: 4px 12px;
  background-color: transparent;
  color: #1e40af;
  border: 1px solid #3b82f6;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.clear-draft-btn:hover {
  background-color: #3b82f6;
  color: white;
}

.error-icon,
.success-icon,
.info-icon {
  font-size: 18px;
  flex-shrink: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .feedback-container {
    padding: 16px;
  }

  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content {
    width: 100%;
  }

  .feedback-form-container {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }
}

/* Dark theme support */
[data-theme="dark"] .feedback-form-container {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .description-editor .ql-toolbar {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .description-editor .ql-container {
  background-color: var(--input-bg);
  color: var(--text-color);
}

[data-theme="dark"] .attachment-item {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .info-message {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border-color: #3b82f6;
}

[data-theme="dark"] .clear-draft-btn {
  color: #60a5fa;
  border-color: #3b82f6;
}

[data-theme="dark"] .clear-draft-btn:hover {
  background-color: #3b82f6;
  color: white;
}

/* Feedback Success Modal Styles */
.feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.feedback-modal-content {
  background-color: var(--content-bg);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  animation: feedbackModalSlideIn 0.3s ease-out;
}

.feedback-modal-header {
  text-align: center;
  padding: 32px 24px 24px;
}

.feedback-modal-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 16px;
}

.feedback-modal-icon.success {
  background-color: var(--success-bg);
  color: var(--success-text);
}

.feedback-modal-header h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

.feedback-modal-header p {
  margin: 0;
  font-size: 16px;
  color: var(--text-muted);
  line-height: 1.5;
}

.feedback-modal-actions {
  display: flex;
  gap: 12px;
  padding: 0 24px 32px;
  justify-content: center;
}

.feedback-modal-btn {
  flex: 1;
  max-width: 160px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.feedback-modal-btn.secondary {
  background-color: var(--section-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.feedback-modal-btn.secondary:hover {
  background-color: var(--border-color);
}

.feedback-modal-btn.primary {
  background: linear-gradient(135deg, var(--primary-green), #00b85e);
  color: white;
}

.feedback-modal-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 207, 106, 0.3);
}

@keyframes feedbackModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dark theme support for feedback modal */
[data-theme="dark"] .feedback-modal-content {
  background-color: var(--section-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .feedback-modal-icon.success {
  background-color: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

[data-theme="dark"] .feedback-modal-btn.secondary {
  background-color: var(--content-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .feedback-modal-btn.secondary:hover {
  background-color: var(--border-color);
}
