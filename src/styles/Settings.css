/* Dark mode variables */
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --section-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #ddd;
  --success-bg: #e7f7ed;
  --success-text: #1d915c;
  --error-bg: #ffeeee;
  --error-text: #d63031;
  --btn-primary-bg: #4caf50;
  --btn-primary-hover: #388e3c;
  --btn-secondary-bg: #f0f0f0;
  --btn-secondary-hover: #e0e0e0;
  --btn-secondary-text: #333333;
  --connection-bg: #f8f9fa;
  --filter-bg: #f8f9fa;
  --filter-option-bg: #e3f2fd;
  --filter-option-text: #1976d2;
  --primary-light: #e3f2fd; /* Added for setting-user-item background */
  --bg-light: #f8f9fa; /* Added for setting-platform-config background */
  --hover-color: #f0f0f0; /* Added for toggle button hover */
}

[data-theme="dark"] {
  --bg-color: #0d1527;
  --text-color: #ffffff;
  --border-color: #333333;
  --section-bg: #1f2937;
  --input-bg: #111827;
  /* --input-bg: #1f2937; */
  --input-border: #444444;
  --success-bg: #1e3a1e;
  --success-text: #4caf50;
  --error-bg: #3a1e1e;
  --error-text: #ff5252;
  --btn-primary-bg: #388e3c;
  --btn-primary-hover: #2e7d32;
  --btn-secondary-bg: #333333;
  --btn-secondary-hover: #444444;
  --btn-secondary-text: #ffffff;
  --connection-bg: #2d2d2d;
  --filter-bg: #2d2d2d;
  --filter-option-bg: #1e3a1e;
  --filter-option-text: #4caf50;
  --primary-light: #1e3a1e; /* Added for setting-user-item background in dark mode */
  --bg-light: #2d2d2d; /* Added for setting-platform-config background in dark mode */
  --hover-color: #444444; /* Added for toggle button hover in dark mode */
}

.settings-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 10px 0;
  /* Remove bottom border from individual headers if sections handle it */
  /* border-bottom: 1px solid var(--border-color); */
}

.section-header h3 {
  margin: 0; /* Reset margin for h3 inside header */
  padding: 0; /* Reset padding */
  border-bottom: none; /* Remove individual border */
  font-size: 18px;
}

.setting-toggle-section-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.setting-toggle-section-btn:hover {
  background-color: var(--hover-color);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  font-size: 24px;
  color: var(--text-color);
}

.settings-actions {
  display: flex;
  gap: 10px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-section {
  background-color: var(--section-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 10px 20px 0px 20px;
  border: 1px solid var(--border-color);
}

/* Styles for Approver/Executor Section */
.setting-approvers-container {
  animation: fadeIn 0.3s ease-in-out;
}

.setting-user-input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid var(--input-border);
  border-radius: 6px;
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 0.97rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  margin-right: 6px;
}
.setting-user-input:focus {
  border-color: var(--filter-option-text);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

/* Styles for user suggestions dropdown */
.user-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
}

.user-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.2s;
}

.user-suggestion-item:hover {
  background-color: var(--hover-color);
}

[data-theme="dark"] .user-suggestions {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.setting-user-add-btn {
  min-width: 32px;
  padding: 6px 12px;
  background: var(--btn-primary-bg);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.setting-user-add-btn:hover {
  background: var(--btn-primary-hover);
}
[data-theme="dark"] .setting-user-input {
  background: var(--input-bg);
  color: var(--text-color);
  border-color: var(--input-border);
}
[data-theme="dark"] .setting-user-input:focus {
  border-color: var(--filter-option-text);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
}
[data-theme="dark"] .setting-user-add-btn {
  background: var(--btn-primary-bg);
  color: #fff;
}
[data-theme="dark"] .setting-user-add-btn:hover {
  background: var(--btn-primary-hover);
}


.setting-request-type-section {
  margin-bottom: 25px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
}

.setting-request-type-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.setting-request-type-section h4 {
  font-size: 16px;
  color: var(--text-color);
  margin-bottom: 15px;
}

.setting-platform-config {
  margin: 15px 0 15px 20px; /* Indent platform config */
  padding: 15px;
  background-color: var(--input-bg);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.setting-platform-config h5 {
  font-size: 14px;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 10px;
  font-weight: 600;
}

.config-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.config-item {
  flex: 1;
  min-width: 250px;
}

.config-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 13px;
  color: var(--text-color);
}

.setting-user-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px; /* Reduced margin */
}

.setting-user-item {
  display: flex;
  align-items: center;
  background-color: var(--primary-light);
  color: var(--filter-option-text); /* Use filter text color for consistency */
  padding: 4px 8px;
  border-radius: 12px; /* Pill shape */
  font-size: 0.85rem;
  font-weight: 500;
}

.setting-user-item span {
  margin-right: 8px;
}

.setting-user-item .btn-remove-option {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.settings-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 10px;
  /* border-bottom: 1px solid var(--border-color); */
  color: var(--text-color);
  font-size: 18px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
/* Theme Toggle Styles */
.setting-theme-toggle {
  display: flex;
  gap: 10px;
}

.setting-theme-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s;
}

.setting-theme-btn.active {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.setting-theme-btn:hover {
  background-color: var(--btn-secondary-hover);
}

/* Connection Settings Styles */
.connections-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--input-bg);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.connection-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.connection-name {
  font-weight: 500;
  color: var(--text-color);
}

.connection-type {
  padding: 2px 8px;
  background-color: var(--input-bg);
  color: var(--filter-option-text);
  border-radius: 12px;
  font-size: 12px;
}

.connection-url {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 14px;
}

.add-connection {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.add-connection h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
}

.connection-form {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: 10px;
  align-items: center;
}

.connection-form input,
.connection-form select {
  padding: 8px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .settings-actions {
    width: 100%;
  }

  .btn-save, .btn-reset {
    flex: 1;
    justify-content: center;
  }

  .connection-form {
    grid-template-columns: 1fr;
  }

  .connection-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .setting-theme-toggle {
    flex-direction: column;
  }
}