import React, { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import './App.css';
import Sidebar from './components/Sidebar';
import NotificationCenter from './components/NotificationCenter';
import Dashboard from './pages/Dashboard';
import Tickets from './pages/Tickets';
// import TicketDetail from './pages/TicketDetail';
import Users from './pages/Users';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import Notifications from './pages/Notifications';
import Login from './pages/Login';
import Welcome from './pages/Welcome';
import UserDetail from './pages/UserDetail';
import Profile from './pages/Profile';
import AskAI from './pages/AskAI';
import WhitelistPlatform from './pages/WhitelistPlatform';
import ReleaseNotes from './pages/ReleaseNotes';
import Feedback from './pages/Feedback';
// import RequestForm from './pages/requests/RequestForm';
import CreateRequest from './pages/requests/CreateRequest';
import PlatformSelect from './pages/requests/PlatformSelect';
import RequestPlatform from './pages/requests/RequestPlatform';
import RequestDatabase from './pages/requests/RequestDatabase';
import RequestAdhoc from './pages/requests/RequestAdhoc';
import RequestReport from './pages/requests/RequestReport';
import RequestDetail from './pages/requests/RequestDetail';
import Requests from './pages/requests/Requests';
// import { testApiConnection } from './services/debug.service';

// Main content component that uses React Router hooks
const MainContent = ({ user, activePage, setActivePage, handleLogout, navigateToTickets }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, language } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const searchRef = useRef(null);

  // Define searchable app features
  const appFeatures = [
    {
      id: 'dashboard',
      title: language === 'en' ? 'Dashboard' : 'Trang chủ',
      description: language === 'en' ? 'View system overview' : 'Xem tổng quan hệ thống',
      path: '/dashboard'
    },
    {
      id: 'create-request',
      title: language === 'en' ? 'Create New Request' : 'Tạo yêu cầu mới',
      description: language === 'en' ? 'Submit a new request' : 'Gửi yêu cầu mới',
      path: '/requests/types',
      action: () => { navigate('/requests/types'); }
    },
    {
      id: 'view-requests',
      title: language === 'en' ? 'View Requests' : 'Xem danh sách yêu cầu',
      description: language === 'en' ? 'Browse all requests' : 'Duyệt tất cả các yêu cầu',
      path: '/requests'
    },
    {
      id: 'whitelist',
      title: language === 'en' ? 'Whitelist Platform' : 'Nền tảng Whitelist',
      description: language === 'en' ? 'Query and manage whitelist data' : 'Truy vấn và quản lý dữ liệu whitelist',
      path: '/whitelist'
    },
    {
      id: 'notifications',
      title: language === 'en' ? 'Notifications' : 'Thông báo',
      description: language === 'en' ? 'View all notifications' : 'Xem tất cả thông báo',
      path: '/notifications'
    },
    {
      id: 'askai',
      title: language === 'en' ? 'Ask AI' : 'Hỏi AI',
      description: language === 'en' ? 'Chat with AI assistant' : 'Trò chuyện với trợ lý AI',
      path: '/askai'
    },
    {
      id: 'settings',
      title: language === 'en' ? 'Settings' : 'Cài đặt',
      description: language === 'en' ? 'Manage system settings' : 'Quản lý cài đặt hệ thống',
      path: '/settings'
    },
    {
      id: 'users',
      title: language === 'en' ? 'User Management' : 'Quản lý người dùng',
      description: language === 'en' ? 'Manage users and permissions' : 'Quản lý người dùng và quyền hạn',
      path: '/users'
    },
    {
      id: 'reports',
      title: language === 'en' ? 'Reports' : 'Báo cáo',
      description: language === 'en' ? 'View system reports and analytics' : 'Xem báo cáo và phân tích hệ thống',
      path: '/reports'
    }
  ];

  // Filter features based on search term
  const filteredFeatures = searchTerm.trim() !== ''
    ? appFeatures.filter(feature =>
        feature.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feature.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setShowSearchResults(e.target.value.trim() !== '');
  };

  // Handle feature selection
  const handleFeatureSelect = (feature) => {
    setSearchTerm('');
    setShowSearchResults(false);
    navigate(feature.path);
    if (feature.action) {
      feature.action();
    }
  };

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [searchRef]);

  // Update active page based on current route
  useEffect(() => {
    const path = location.pathname.split('/')[1] || 'dashboard';
    setActivePage(path);
  }, [location, setActivePage]);

  // Handle navigation to create request page
  const navigateToCreateRequest = () => {
    navigate('/requests/types');
  };

  return (
    <main className="content">
      <div className="content-header">
        <h1>{activePage.charAt(0).toUpperCase() + activePage.slice(1)}</h1>
        <div className="header-actions">
          <button className="btn-primary" onClick={navigateToCreateRequest}>+ New Request</button>
          <div className="search-box" ref={searchRef}>
            <input
              type="text"
              placeholder={language === 'en' ? "Search features..." : "Tìm kiếm chức năng..."}
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <i className="search-icon"></i>

            {showSearchResults && filteredFeatures.length > 0 && (
              <div className="search-results">
                {filteredFeatures.map(feature => (
                  <div
                    key={feature.id}
                    className="search-result-item"
                    onClick={() => handleFeatureSelect(feature)}
                  >
                    <div className="search-result-title">{feature.title}</div>
                    <div className="search-result-description">{feature.description}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <NotificationCenter user={user} />
        </div>
      </div>
      <div className="content-body">
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard navigateToTickets={navigateToTickets} />} />
          <Route path="/tickets" element={<Tickets />} />
          {/* <Route path="/ticket" element={<TicketDetail />} /> */}
          <Route path="/users" element={user && user.role === 'admin' ? <Users /> : <Navigate to="/dashboard" replace />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/userdetail" element={<UserDetail />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/notifications" element={<Notifications />} />
          <Route path="/askai" element={<AskAI />} />
          <Route path="/whitelist" element={<WhitelistPlatform />} />
          <Route path="/release-notes" element={<ReleaseNotes />} />
          <Route path="/feedback" element={<Feedback />} />

          {/* Request routes */}
          <Route path="/requests/types" element={<CreateRequest />} />
          <Route path="/requests/platform-select" element={<PlatformSelect />} />
          {/* <Route path="/request/create" element={<RequestForm />} /> */}
          <Route path="/requests/platform" element={<RequestPlatform />} />
          <Route path="/requests/database" element={<RequestDatabase />} />
          <Route path="/requests/adhoc" element={<RequestAdhoc />} />
          <Route path="/requests/report" element={<RequestReport />} />
          <Route path="/requests/detail" element={<RequestDetail />} />
          <Route path="/requests" element={<Requests />} />
        </Routes>
      </div>
    </main>
  );
};

function App() {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showWelcome, setShowWelcome] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });

  const [activePage, setActivePage] = useState(() => {
    const savedPage = localStorage.getItem('activePage');
    return savedPage || 'dashboard';
  });

  // Kiểm tra token khi ứng dụng khởi động
  useEffect(() => {
    const checkAuth = () => {
      const userData = localStorage.getItem('user');

      if (userData) {
        const parsedUser = JSON.parse(userData);

        // Kiểm tra thời gian hết hạn của token
        if (parsedUser.expiresAt && new Date(parsedUser.expiresAt) > new Date()) {
          setUser(parsedUser);

          // Check if we need to show welcome page
          if (parsedUser.loginType && parsedUser.loginType !== 'no-change') {
            setShowWelcome(true);
          }
        } else {
          // Token đã hết hạn, xóa khỏi localStorage
          localStorage.removeItem('user');
          setUser(null);
        }
      }

      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const handlePageChange = (page) => {
    setActivePage(page);
    localStorage.setItem('activePage', page);
  };

  // Hàm để chuyển đến trang Tickets với bộ lọc
  const navigateToTickets = (filterStatus) => {
    // Lưu trạng thái bộ lọc vào localStorage
    localStorage.setItem('ticketFilterStatus', filterStatus);
    // Chuyển đến trang Tickets
    handlePageChange('tickets');
  };

  // Xử lý đăng nhập thành công
  const handleLogin = (userData) => {
    setUser(userData);

    // Check if we need to show welcome page
    if (userData.loginType && userData.loginType !== 'no-change') {
      setShowWelcome(true);
    }
  };

  // Handle welcome completion
  const handleWelcomeComplete = (updatedUser) => {
    setUser(updatedUser);
    setShowWelcome(false);
  };

  // Xử lý đăng xuất
  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('activePage');
    setUser(null);
    setActivePage('dashboard');
    setShowWelcome(false);
  };

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    localStorage.setItem('sidebarCollapsed', JSON.stringify(newState));
  };

  // Wrap everything in ThemeProvider including loading screen
  return (
    <ThemeProvider>
      {isLoading ? (
        // Hiển thị loading khi đang kiểm tra xác thực
        <div className="loading-screen">Đang tải...</div>
      ) : (
        // Nếu người dùng chưa đăng nhập, hiển thị trang Login
        !user ? (
          <Login onLogin={handleLogin} />
        ) : (
          <Router>
            {showWelcome ? (
              <Welcome user={user} onComplete={handleWelcomeComplete} />
            ) : (
              <div className={`app-container ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
                <Sidebar
                  activePage={activePage}
                  setActivePage={handlePageChange}
                  user={user}
                  onLogout={handleLogout}
                  isCollapsed={sidebarCollapsed}
                  toggleSidebar={toggleSidebar}
                />
                <MainContent
                  user={user}
                  activePage={activePage}
                  setActivePage={setActivePage}
                  handleLogout={handleLogout}
                  navigateToTickets={navigateToTickets}
                />
              </div>
            )}
          </Router>
        )
      )}
    </ThemeProvider>
  );
}

export default App;
