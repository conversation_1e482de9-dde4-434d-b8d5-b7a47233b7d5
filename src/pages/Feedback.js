import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  FiArrowLeft,
  FiSend,
  FiAlertCircle,
  FiCheckCircle,
  FiPaperclip,
  FiX,
  FiMessageCircle,
} from "react-icons/fi";
import { useTheme } from "../context/ThemeContext";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import BasicQuillEditor from "../components/BasicQuillEditor";
import "../styles/Feedback.css";

const Feedback = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [draftRestored, setDraftRestored] = useState(false);
  const [attachmentError, setAttachmentError] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const errorTimeoutRef = useRef(null);
  const attachmentErrorTimeoutRef = useRef(null);

  const [formData, setFormData] = useState({
    subject: "",
    category: "",
    description: "",
    priority: "medium",
  });

  const [attachments, setAttachments] = useState([]);
  const fileInputRef = useRef(null);

  // Keep track of uploaded files for cleanup
  const uploadedFilesRef = useRef([]);

  // Track if we have a saved draft
  const hasDraftRef = useRef(false);

  // Track if form was just submitted successfully to prevent auto-save
  const justSubmittedRef = useRef(false);

  // Draft key for localStorage
  const DRAFT_KEY = 'feedback-draft';

  // Save draft to localStorage
  const saveDraft = () => {
    try {
      const draft = {
        formData,
        attachments: attachments.map(att => ({
          id: att.id,
          uploadedFileId: att.uploadedFileId,
          name: att.name,
          size: att.size,
          status: att.status,
          errorMessage: att.errorMessage
        })),
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(DRAFT_KEY, JSON.stringify(draft));
      hasDraftRef.current = true;
    } catch (error) {
      console.error('Error saving draft:', error);
    }
  };

  // Clear draft from localStorage
  const clearDraft = () => {
    try {
      console.log('Clearing draft from localStorage...');
      localStorage.removeItem(DRAFT_KEY);
      hasDraftRef.current = false;
      console.log('Draft cleared successfully');
    } catch (error) {
      console.error('Error clearing draft:', error);
    }
  };

  // Clear draft and reset form
  const clearDraftAndReset = () => {
    clearDraft();
    setFormData({
      subject: "",
      category: "",
      description: "",
      priority: "medium",
    });
    setAttachments([]);
    setDraftRestored(false);
  };

  // Restore draft from localStorage
  const restoreDraft = async () => {
    try {
      const savedDraft = localStorage.getItem(DRAFT_KEY);
      if (!savedDraft) return;

      const draft = JSON.parse(savedDraft);

      // Check if draft is not too old (e.g., 24 hours)
      const draftAge = new Date() - new Date(draft.timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      if (draftAge > maxAge) {
        clearDraft();
        return;
      }

      // Check if there's actually content to restore
      const hasFormContent = draft.formData && (
        draft.formData.subject.trim() ||
        draft.formData.category ||
        draft.formData.description.trim()
      );
      const hasAttachments = draft.attachments && draft.attachments.some(att =>
        att.status === 'uploaded' && att.uploadedFileId
      );

      if (hasFormContent || hasAttachments) {
        // Restore form data
        if (draft.formData) {
          setFormData(draft.formData);
        }

        // Restore attachments (only uploaded ones)
        if (draft.attachments && draft.attachments.length > 0) {
          const validAttachments = draft.attachments.filter(att =>
            att.status === 'uploaded' && att.uploadedFileId
          );

          if (validAttachments.length > 0) {
            setAttachments(validAttachments);
          }
        }

        // Show draft restored notification
        setDraftRestored(true);
        setTimeout(() => {
          setDraftRestored(false);
        }, 10000); // Hide after 10 seconds
      }
    } catch (error) {
      console.error('Error restoring draft:', error);
      clearDraft();
    }
  };

  // Restore draft on component mount
  useEffect(() => {
    // Check if draft exists first
    const savedDraft = localStorage.getItem(DRAFT_KEY);
    if (savedDraft) {
      hasDraftRef.current = true;
      restoreDraft();
    }
  }, []);

  // Auto-save draft when form data or attachments change
  useEffect(() => {
    // Don't auto-save if form was just submitted successfully
    if (justSubmittedRef.current) {
      return;
    }

    // Check if there's any form content
    const hasFormContent = formData.subject.trim() ||
                          formData.category ||
                          formData.description.trim();

    // Always save if there's form content, or if there was a previous draft
    // This ensures we update the draft even when removing the last file
    const shouldSave = hasFormContent || attachments.length > 0 || hasDraftRef.current;

    if (shouldSave) {
      // Debounce the save operation
      const timeoutId = setTimeout(() => {
        // Double check if form wasn't submitted during the timeout
        if (justSubmittedRef.current) {
          return;
        }

        // If no content at all, clear the draft instead of saving empty
        if (!hasFormContent && attachments.length === 0) {
          console.log('Auto-save: No content, clearing draft');
          clearDraft();
        } else {
          console.log('Auto-save: Saving draft');
          saveDraft();
        }
      }, 1000); // Save after 1 second of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [formData, attachments]);

  // Update ref when files are uploaded
  useEffect(() => {
    const uploadedFiles = attachments.filter(att => att.status === 'uploaded' && att.uploadedFileId);
    uploadedFilesRef.current = uploadedFiles.map(att => ({
      id: att.uploadedFileId,
      name: att.name
    }));
  }, [attachments]);

  // Cleanup uploaded files when component unmounts or page refreshes
  useEffect(() => {
    // Handle page refresh/close
    const handleBeforeUnload = () => {
      const filesToDelete = uploadedFilesRef.current;

      if (filesToDelete.length > 0) {
        // Get token from user data in localStorage
        const userStr = localStorage.getItem('user');
        let token = null;
        if (userStr) {
          try {
            const user = JSON.parse(userStr);
            token = user.token;
          } catch (error) {
            console.error('Error parsing user data:', error);
          }
        }

        if (token) {
          // Use fetch with keepalive for reliable cleanup on page unload
          filesToDelete.forEach((file) => {
            try {
              const url = `${process.env.REACT_APP_API_URL || 'http://localhost:3014'}${ENDPOINTS.FILES.DELETE(file.id)}`;

              // Use fetch with keepalive flag to ensure request completes even if page unloads
              fetch(url, {
                method: 'DELETE',
                keepalive: true,
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              }).catch(error => {
                console.error(`Cleanup on unload: Error deleting file ${file.name}:`, error);
              });
            } catch (error) {
              console.error(`Cleanup on unload: Error deleting file ${file.name}:`, error);
            }
          });
        }
      }
    };

    // Add event listener for page unload
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      // Remove event listener
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // Clear timeouts
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
      if (attachmentErrorTimeoutRef.current) {
        clearTimeout(attachmentErrorTimeoutRef.current);
      }

      // Cleanup function - delete all uploaded files when component unmounts
      const filesToDelete = uploadedFilesRef.current;

      if (filesToDelete.length > 0) {
        // Use Promise.all to properly handle async operations in cleanup
        Promise.all(
          filesToDelete.map(async (file) => {
            try {
              await apiService.delete(ENDPOINTS.FILES.DELETE(file.id));
              console.log(`Cleanup: File ${file.name} deleted from server`);
            } catch (error) {
              console.error(`Cleanup: Error deleting file ${file.name}:`, error);
            }
          })
        );
      }
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // File validation constants
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];

  // Category options
  const categoryOptions = [
    { value: "bug", label: "Bug Report" },
    { value: "feature", label: "Feature Request" },
    { value: "improvement", label: "Improvement Suggestion" },
    { value: "general", label: "General Feedback" },
    { value: "support", label: "Support Request" },
    { value: "other", label: "Other" },
  ];

  // Priority options
  const priorityOptions = [
    { value: "low", label: "Low" },
    { value: "medium", label: "Medium" },
    { value: "high", label: "High" },
    { value: "urgent", label: "Urgent" },
  ];

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle description change from Quill editor
  const handleDescriptionChange = (content) => {
    setFormData((prev) => ({
      ...prev,
      description: content,
    }));
  };

  // Validate file
  const validateFile = (file) => {
    if (file.size > MAX_FILE_SIZE) {
      return `File "${file.name}" is too large. Maximum size is 10MB.`;
    }
    if (!ALLOWED_TYPES.includes(file.type)) {
      return `File "${file.name}" type is not allowed. Please use: JPG, PNG, GIF, PDF, DOC, DOCX, or TXT files.`;
    }
    return null;
  };

  // Handle file attachment
  const handleFileSelect = async (e) => {
    const files = Array.from(e.target.files);

    // Clear any previous attachment error
    setAttachmentError(null);

    // Validate each file
    for (const file of files) {
      const validationError = validateFile(file);
      if (validationError) {
        setAttachmentError(validationError);
        clearAttachmentError();
        return;
      }
    }

    // Create attachment objects and add to state
    const newAttachments = files.map((file) => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      status: 'uploading', // Start uploading immediately
      uploadProgress: 0,
      uploadedFileId: null,
      errorMessage: null,
    }));

    setAttachments((prev) => [...prev, ...newAttachments]);

    // Upload each file immediately
    newAttachments.forEach((attachment) => {
      uploadSingleFile(attachment);
    });

    // Clear the input so the same file can be selected again
    e.target.value = '';

    // Clear any attachment error since files are now being processed
    setAttachmentError(null);
  };

  // Delete all uploaded files from server
  const deleteAllUploadedFiles = async () => {
    const uploadedFiles = attachments.filter(att => att.status === 'uploaded' && att.uploadedFileId);

    if (uploadedFiles.length === 0) return;

    // Delete all uploaded files from server
    const deletePromises = uploadedFiles.map(async (attachment) => {
      try {
        await apiService.delete(ENDPOINTS.FILES.DELETE(attachment.uploadedFileId));
        console.log(`File ${attachment.name} deleted from server`);
      } catch (error) {
        console.error(`Error deleting file ${attachment.name}:`, error);
      }
    });

    // Wait for all deletions to complete
    await Promise.all(deletePromises);

    // Clear the cleanup ref since we've manually deleted all files
    uploadedFilesRef.current = [];
  };

  // Remove attachment and delete uploaded file
  const removeAttachment = async (id) => {
    const attachment = attachments.find(att => att.id === id);

    // If file was successfully uploaded, delete it from server
    if (attachment && attachment.status === 'uploaded' && attachment.uploadedFileId) {
      try {
        await apiService.delete(ENDPOINTS.FILES.DELETE(attachment.uploadedFileId));
        console.log(`File ${attachment.name} deleted from server`);

        // Remove from cleanup ref since we've manually deleted it
        uploadedFilesRef.current = uploadedFilesRef.current.filter(
          file => file.id !== attachment.uploadedFileId
        );
      } catch (error) {
        console.error(`Error deleting file ${attachment.name}:`, error);
        // Still remove from UI even if server deletion fails
      }
    }

    // Remove from UI
    setAttachments((prev) => prev.filter((att) => att.id !== id));
  };

  // Upload single file immediately when selected
  const uploadSingleFile = async (attachment) => {
    try {
      const formData = new FormData();
      formData.append('file', attachment.file);
      formData.append('description', `Feedback attachment: ${attachment.name}`);

      const response = await apiService.postFormData(
        ENDPOINTS.FILES.UPLOAD,
        formData,
        (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setAttachments(prev => prev.map(att =>
            att.id === attachment.id
              ? { ...att, uploadProgress: percentCompleted }
              : att
          ));
        }
      );

      // Update status to uploaded
      setAttachments(prev => prev.map(att =>
        att.id === attachment.id
          ? {
              ...att,
              status: 'uploaded',
              uploadProgress: 100,
              uploadedFileId: response.file_id || response.id,
              errorMessage: null
            }
          : att
      ));

    } catch (error) {
      console.error(`Error uploading file ${attachment.name}:`, error);

      // Update status to error with specific error message
      const errorMessage = error.response?.data?.message || error.message || 'Upload failed';
      setAttachments(prev => prev.map(att =>
        att.id === attachment.id
          ? {
              ...att,
              status: 'error',
              uploadProgress: 0,
              errorMessage: errorMessage
            }
          : att
      ));
    }
  };

  // Get uploaded files for form submission
  const getUploadedFiles = () => {
    return attachments
      .filter(att => att.status === 'uploaded')
      .map(att => ({
        id: att.uploadedFileId,
        name: att.name,
        size: att.size,
        original_name: att.name
      }));
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Handle navigation with file cleanup
  const handleNavigation = async (path) => {
    // Delete all uploaded files before navigating away
    await deleteAllUploadedFiles();
    // Clear draft when user explicitly navigates away
    clearDraft();
    navigate(path);
  };

  // Clear error after timeout
  const clearError = () => {
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }
    errorTimeoutRef.current = setTimeout(() => {
      setError(null);
    }, 5000);
  };

  // Clear attachment error after timeout
  const clearAttachmentError = () => {
    if (attachmentErrorTimeoutRef.current) {
      clearTimeout(attachmentErrorTimeoutRef.current);
    }
    attachmentErrorTimeoutRef.current = setTimeout(() => {
      setAttachmentError(null);
    }, 5000);
  };

  // Handle success modal actions
  const handleGoToHome = () => {
    setShowSuccessModal(false);
    navigate('/');
  };

  const handleSendNewFeedback = () => {
    setShowSuccessModal(false);
    // Reset the flag to allow auto-save again
    justSubmittedRef.current = false;
    // Form is already reset, just hide success state
    setSuccess(false);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validation
    if (!formData.subject.trim()) {
      setError("Subject is required");
      setLoading(false);
      clearError();
      return;
    }

    if (!formData.category) {
      setError("Please select a category");
      setLoading(false);
      clearError();
      return;
    }

    if (!formData.description.trim()) {
      setError("Description is required");
      setLoading(false);
      clearError();
      return;
    }

    try {
      // Check if there are any files still uploading
      const uploadingFiles = attachments.filter(att => att.status === 'uploading');
      if (uploadingFiles.length > 0) {
        setError("Please wait for all files to finish uploading before submitting.");
        setLoading(false);
        clearError();
        return;
      }

      // Check if there are any failed uploads
      const failedFiles = attachments.filter(att => att.status === 'error');
      if (failedFiles.length > 0) {
        setError("Please remove failed file uploads or try uploading them again.");
        setLoading(false);
        clearError();
        return;
      }

      // Get successfully uploaded files
      const uploadedFiles = getUploadedFiles();

      // Prepare form data for submission
      const feedbackData = {
        subject: formData.subject.trim(),
        category: formData.category,
        description: formData.description,
        priority: formData.priority,
        attachments: uploadedFiles,
      };

      const response = await apiService.post(
        ENDPOINTS.FEEDBACK.CREATE,
        feedbackData
      );

      if (response) {
        // Set flag to prevent auto-save after successful submission
        justSubmittedRef.current = true;

        // Clear draft since feedback was successfully submitted
        clearDraft();

        // Reset form
        setFormData({
          subject: "",
          category: "",
          description: "",
          priority: "medium",
        });
        // Clear attachments without triggering cleanup (files are now part of the feedback)
        setAttachments([]);
        // Clear cleanup ref since files are now part of submitted feedback
        uploadedFilesRef.current = [];

        // Show success modal instead of success message
        setShowSuccessModal(true);
      }
    } catch (err) {
      console.error("Error submitting feedback:", err);
      if (err.response && err.response.data) {
        setError(err.response.data.message || "Failed to submit feedback");
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      clearError();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="feedback-container">
      <div className="feedback-header">
        <button
          className="back-button"
          onClick={() => handleNavigation("/dashboard")}
          title="Back to Dashboard"
        >
          <FiArrowLeft size={20} />
        </button>
        <div className="header-content">
          <div className="header-icon">
            <FiMessageCircle size={32} />
          </div>
          <div className="header-text">
            <h1>Send Feedback</h1>
            <p>Help us improve by sharing your thoughts and suggestions</p>
          </div>
        </div>
      </div>

      <div className="feedback-form-container">
        {error && (
          <div className="error-message">
            <FiAlertCircle className="error-icon" />
            <span>{error}</span>
          </div>
        )}

        {success && (
          <div className="success-message">
            <FiCheckCircle className="success-icon" />
            <span>Feedback submitted successfully! Thank you for your input.</span>
          </div>
        )}

        {draftRestored && (
          <div className="info-message">
            <FiCheckCircle className="info-icon" />
            <span>Draft restored from previous session. Your work has been recovered.</span>
            <button
              type="button"
              className="clear-draft-btn"
              onClick={clearDraftAndReset}
              title="Clear draft and start fresh"
            >
              Clear Draft
            </button>
          </div>
        )}

        <form onSubmit={handleSubmit} className="feedback-form">
          <div className="form-group">
            <label htmlFor="subject">Subject *</label>
            <input
              type="text"
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              placeholder="Brief summary of your feedback"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="category">Category *</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
              >
                <option value="">Select a category</option>
                {categoryOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="priority">Priority</label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
              >
                {priorityOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description *</label>
            <div className="description-editor">
              <BasicQuillEditor
                value={formData.description}
                onChange={handleDescriptionChange}
                placeholder="Please provide detailed information about your feedback..."
              />
            </div>
          </div>

          <div className="form-group">
            <label>Attachments (optional)</label>
            <div className="attachment-section">
              <button
                type="button"
                className="attachment-button"
                onClick={() => fileInputRef.current?.click()}
              >
                <FiPaperclip size={16} />
                Add Files
              </button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileSelect}
                style={{ display: "none" }}
                accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt"
              />

              {attachmentError && (
                <div className="attachment-error-message">
                  <FiAlertCircle className="attachment-error-icon" />
                  <span>{attachmentError}</span>
                </div>
              )}

              {attachments.length > 0 && (
                <div className="attachments-list">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className={`attachment-item ${attachment.status}`}>
                      <div className="attachment-info">
                        <span className="attachment-name">{attachment.name}</span>
                        <span className="attachment-size">
                          ({formatFileSize(attachment.size)})
                        </span>
                        {attachment.status === 'uploading' && (
                          <span className="upload-progress">
                            {attachment.uploadProgress}%
                          </span>
                        )}
                        {attachment.status === 'uploaded' && (
                          <span className="upload-status success">✓ Uploaded</span>
                        )}
                        {attachment.status === 'error' && (
                          <span className="upload-status error">✗ Failed</span>
                        )}
                      </div>

                      {attachment.status === 'uploading' && (
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${attachment.uploadProgress}%` }}
                          ></div>
                        </div>
                      )}

                      {attachment.status === 'error' && attachment.errorMessage && (
                        <div className="file-error-message">
                          <span className="error-text">{attachment.errorMessage}</span>
                          <button
                            type="button"
                            className="retry-upload-btn"
                            onClick={() => uploadSingleFile(attachment)}
                            title="Retry upload"
                          >
                            Retry
                          </button>
                        </div>
                      )}

                      <button
                        type="button"
                        className="remove-attachment"
                        onClick={() => removeAttachment(attachment.id)}
                        disabled={attachment.status === 'uploading'}
                      >
                        <FiX size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={() => handleNavigation("/dashboard")}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading || attachments.some(att => att.status === 'uploading')}
            >
              {loading ? (
                <>
                  Sending...
                </>
              ) : attachments.some(att => att.status === 'uploading') ? (
                <>
                  Files uploading...
                </>
              ) : (
                <>
                  <FiSend size={16} />
                  Send Feedback
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="feedback-modal-overlay">
          <div className="feedback-modal-content">
            <div className="feedback-modal-header">
              <div className="feedback-modal-icon success">
                <FiCheckCircle size={48} />
              </div>
              <h2>Feedback Submitted Successfully!</h2>
              <p>Thank you for your feedback. We appreciate your input and will review it carefully.</p>
            </div>

            <div className="feedback-modal-actions">
              <button
                type="button"
                className="feedback-modal-btn secondary"
                onClick={handleGoToHome}
              >
                Go to Home
              </button>
              <button
                type="button"
                className="feedback-modal-btn primary"
                onClick={handleSendNewFeedback}
              >
                Send New Feedback
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Feedback;
